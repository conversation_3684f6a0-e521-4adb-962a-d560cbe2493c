"""
OrchestratorEmployee - A specialized agent for coordinating task delegation and workflow between specialist agents.

This module provides an OrchestratorEmployee class that serves as a strategic workflow coordinator
in multi-agent systems. It follows the established agent patterns in the codebase while
focusing specifically on analyzing queries, delegating tasks to appropriate specialist agents,
and managing the overall workflow orchestration.
"""

from __future__ import annotations

import logging
from typing import List, Optional, Sequence

from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.conditions import SourceMatchTermination, TextMentionTermination
from autogen_agentchat.messages import (
    BaseAgentEvent,
    BaseChatMessage,
    StructuredMessage,
)
from autogen_agentchat.teams import SelectorGroupChat
from autogen_core.memory import ListMemory
from autogen_core.model_context import BufferedChatCompletionContext
from autogen_core.models import ChatCompletionClient

from ...shared.config.base import Settings, get_settings
from ...utils.agent_response import (
    AgentResponse,
    AgentSelectionResponse,
    KnowledgeStructuredResponse,
)
from ...utils.enums import Mode, Resources
from ..model_factory import ModelFactory
from .global_act.discovery_master_agent import DiscoveryMasterEmployee
from .global_act.tool_calling_agent import Tool<PERSON><PERSON>ingEmployee
from .global_ask.general_agent import GeneralKnowledgeEmployee
from .global_ask.knowledge_base_agent import KnowledgeBaseEmployee
from .global_ask.summary_agent import SummaryEmployee
from .global_ask.web_search_agent import WebSearchEmployee

logger = logging.getLogger(__name__)


class OrchestratorEmployee:
    """
    A specialized orchestrator agent that coordinates task delegation and workflow between specialist agents.

    This agent is designed to:
    - Analyze user queries thoroughly for complexity, ambiguity, and requirements
    - Make strategic decisions about task delegation to specialist agents
    - Manage workflow sequences and coordination between multiple agents
    - Provide clear rationale for delegation decisions and next steps
    - Handle error recovery and workflow adaptations
    - Maintain conversation context and state throughout complex workflows
    - Never answer queries directly - focuses exclusively on coordination
    """

    def __init__(
        self,
        resources: Resources = Resources.ALL,
        mode: Mode = Mode.ASK,
        use_tool: bool = False,
    ):
        """Initialize the OrchestratorEmployee with default configuration."""
        self._settings: Settings = get_settings()
        self._model_client: Optional[ChatCompletionClient] = None
        self._agent: Optional[AssistantAgent] = None
        self._is_initialized: bool = False
        self._resources: Resources = resources
        self._mode: Mode = mode
        self._use_tool: bool = use_tool

    async def initialize(
        self,
        memory: Optional[ListMemory] = [],
        resources: Resources = Resources.ALL,
        mode: Mode = Mode.ASK,
        use_tool: bool = False,
    ) -> bool:
        """
        Initialize the orchestrator agent with model client.

        Returns:
            bool: True if initialization successful, False otherwise
        """
        # Update resources if provided
        if resources != Resources.ALL:
            self._resources = resources

        # Update mode if provided
        if mode != Mode.ASK:
            self._mode = mode

        # Update use_tool if provided
        self._use_tool = use_tool

        try:
            # Create model client using ModelFactory
            model_config = {
                "provider": "OpenAIChatCompletionClient",
                "llm_type": "openai",
                "model": "gpt-4.1",
                "api_key": self._settings.requesty.api_key,
                "base_url": self._settings.requesty.base_url,
                "model_info": {
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,
                },
            }

            self._model_client = ModelFactory.create_model_client(model_config)
            if not self._model_client:
                logger.error("Failed to create model client for OrchestratorEmployee")
                return False

            # Create model context without persistent memory
            model_context = BufferedChatCompletionContext(buffer_size=8)

            # Create the assistant agent (no external tools needed)
            self._agent = AssistantAgent(
                name="OrchestratorEmployee",
                description="Coordinates task delegation and workflow between a team of employees. Never answers queries directly. This employee should be the first to engage when given a new task.",
                model_client=self._model_client,
                reflect_on_tool_use=False,  # No tools to reflect on
                model_context=model_context,
                memory=[memory],
                system_message=self._get_enhanced_system_message(
                    self._resources, self._mode, self._use_tool
                ),
                # model_client_stream=True,
                output_content_type=AgentResponse,
            )

            self._is_initialized = True
            logger.info("OrchestratorEmployee initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize OrchestratorEmployee: {str(e)}")
            self._is_initialized = False
            return False

    def _get_enhanced_system_message(
        self,
        resources: Resources = Resources.ALL,
        mode: Mode = Mode.ASK,
        use_tool: bool = False,
    ) -> str:
        print(f"resources: {resources}, mode: {mode}, use_tool: {use_tool}")
        """
        Get the enhanced system message for the orchestrator agent.

        Args:
            resources: The resources configuration controlling which employees are available
            mode: The operational mode controlling which mode sections are available

        Returns:
            str: Comprehensive system message for orchestration operations
        """
        return (
            """
            You are a Strategic Workflow Orchestrator with expertise in coordinating complex multi-employee task execution.

            OPERATIONAL MODES:
            """
            + (
                """The system operates in ASK mode for information gathering and knowledge synthesis.

            **ASK MODE** - Information Gathering & Knowledge Synthesis:
            - **Trigger**: When user mentions "ask" mode or queries requiring information synthesis
            - **Leadership**: Orchestrator leads the ASK mode workflow"""
                + (
                    """
            - **Available Employees**: KnowledgeBaseEmployee (PRIMARY), GeneralKnowledgeEmployee, SummaryEmployee
            - **Flow**: Orchestrator → [KnowledgeBaseEmployee (FIRST PRIORITY for organizational content) | GeneralKnowledgeEmployee (fallback)] → SummaryEmployee
            - **Priority Strategy**: ALWAYS try KnowledgeBaseEmployee FIRST for any organizational, policy, document, tool, or internal procedure queries
            - **Knowledge Base Priority**: When content is available in knowledge base, use it exclusively. Only use GeneralKnowledgeEmployee when knowledge base has no relevant content"""
                    if resources == Resources.ORGANIZATION
                    else (
                        """
            - **Available Employees**: KnowledgeBaseEmployee (PRIMARY for organizational content), GeneralKnowledgeEmployee, WebSearchEmployee, SummaryEmployee
            - **Flow**: Orchestrator → [KnowledgeBaseEmployee (FIRST for organizational queries) | GeneralKnowledgeEmployee | WebSearchEmployee] → SummaryEmployee
            - **Priority Strategy**:
              * For organizational/policy/document/tool queries → KnowledgeBaseEmployee FIRST
              * For current events/recent information → WebSearchEmployee
              * For general knowledge → GeneralKnowledgeEmployee
              * Always prioritize KnowledgeBaseEmployee for any potentially organizational content"""
                        if resources == Resources.ALL
                        else """
            - **Available Employees**: GeneralKnowledgeEmployee"""
                        + (
                            ", WebSearchEmployee"
                            if resources != Resources.ORGANIZATION
                            else ""
                        )
                        + (
                            ", KnowledgeBaseEmployee"
                            if resources != Resources.RESEARCH
                            else ""
                        )
                        + """, SummaryEmployee
            - **Flow**: Orchestrator → [GeneralKnowledgeEmployee"""
                        + (
                            " | WebSearchEmployee"
                            if resources != Resources.ORGANIZATION
                            else ""
                        )
                        + (
                            " | KnowledgeBaseEmployee"
                            if resources != Resources.RESEARCH
                            else ""
                        )
                        + """] → SummaryEmployee"""
                    )
                )
                + """
            - **Final Response**: Delivered by SummaryEmployee
            - **Use Cases**: Research queries, fact-finding, information synthesis, knowledge requests"""
                if mode == Mode.ASK
                else ""
            )
            + (
                """The system operates in ACT mode for action-oriented task execution.

            **ACT MODE** - Action-Oriented Task Execution:
            - **Trigger**: When user mentions "act" mode or requests task execution/actions"""
                + (
                    """
            - **Leadership**: First delegate to ToolCallingEmployee, then to DiscoveryMasterEmployee if needed
            - **Available Employees**: ToolCallingEmployee, DiscoveryMasterEmployee, KnowledgeBaseEmployee (PRIMARY), GeneralKnowledgeEmployee
            - **Information Gathering Priority**: ALWAYS use KnowledgeBaseEmployee FIRST for organizational context, policies, documents, tools, and internal procedures before task execution"""
                    if resources == Resources.ORGANIZATION
                    else (
                        """
            - **Leadership**: First delegate to ToolCallingEmployee, then to DiscoveryMasterEmployee if needed
            - **Available Employees**: ToolCallingEmployee, DiscoveryMasterEmployee, KnowledgeBaseEmployee (PRIMARY for organizational content), GeneralKnowledgeEmployee, WebSearchEmployee
            - **Information Gathering Priority**:
              * For organizational context/policies/documents/tools → KnowledgeBaseEmployee FIRST
              * For current information needs → WebSearchEmployee
              * For general knowledge → GeneralKnowledgeEmployee
              * Always prioritize KnowledgeBaseEmployee for any organizational content before task execution"""
                        if resources == Resources.ALL
                        else """
            - **Leadership**: First delegate to ToolCallingEmployee, then to DiscoveryMasterEmployee if needed
            - **Available Employees**: ToolCallingEmployee, DiscoveryMasterEmployee, GeneralKnowledgeEmployee"""
                        + (
                            ", WebSearchEmployee"
                            if resources != Resources.ORGANIZATION
                            else ""
                        )
                        + (
                            ", KnowledgeBaseEmployee"
                            if resources != Resources.RESEARCH
                            else ""
                        )
                    )
                )
                + """
            - **Flow**: Orchestrator → ToolCallingEmployee → [DiscoveryMasterEmployee if needed] → [Other employees as needed]
            - **Orchestrator Role**: Manage tool calling workflow and delegation
            - **Final Response**: Delivered by ToolCallingEmployee or DiscoveryMasterEmployee"""
                if use_tool
                else """
            - **Leadership**: Hand over to DiscoveryMasterEmployee (ACT mode leader)
            - **Available Employees**: DiscoveryMasterEmployee, GeneralKnowledgeEmployee"""
                + (", WebSearchEmployee" if resources != Resources.ORGANIZATION else "")
                + (", KnowledgeBaseEmployee" if resources != Resources.RESEARCH else "")
                + """
            - **Flow**: Orchestrator → DiscoveryMasterEmployee → [Other employees as needed]
            - **Orchestrator Role**: Full delegation to DiscoveryMasterEmployee
            - **Final Response**: Delivered by DiscoveryMasterEmployee"""
            )
            + """
            - **Use Cases**: Task execution, process automation, workflow management, action implementation"""
            if mode == Mode.ACT
            else ""
            + """

            """
            + (
                """MODE DETECTION GUIDELINES:
            - **ASK Mode Active**: Currently operating in ASK mode for information gathering and synthesis
            - **Focus**: Research queries, fact-finding, information synthesis, knowledge requests
            - DO NOT mention the mode that has been selected by the user in the conversation"""
                if mode == Mode.ASK
                else ""
            )
            + (
                """MODE DETECTION GUIDELINES:
            - **ACT Mode Active**: Currently operating in ACT mode for action-oriented task execution
            - **Focus**: Task execution, process automation, workflow management, action implementation
            - DO NOT mention the mode that has been selected by the user in the conversation"""
                if mode == Mode.ACT
                else ""
            )
            + """

            MODE-SPECIFIC DELEGATION PATTERNS:
            """
            + (
                """
            **ASK Mode Delegation**:
            1. Analyze information requirements (current vs. historical, scope, depth)
            2. Delegate to appropriate information gathering employee:
               - Historical/established facts → GeneralKnowledgeEmployee"""
                + (
                    """
               - Current events/real-time data → WebSearchEmployee"""
                    if resources != Resources.ORGANIZATION
                    else ""
                )
                + (
                    """
               - Organizational/policies/documents/tools/organisational internal procedures → KnowledgeBaseEmployee"""
                    if resources != Resources.RESEARCH
                    else ""
                )
                + """
            3. Coordinate sequential workflow if multiple information sources needed
            4. Delegate to SummaryEmployee for final synthesis and user-facing response
            5. Orchestrator maintains workflow oversight throughout"""
                if mode == Mode.ASK
                else ""
            )
            + (
                """
            **ACT Mode Delegation**:"""
                + (
                    """
            1. Initial analysis of action requirements and information needs
            2. Gather necessary information using appropriate employees BEFORE tool execution:
               - Organizational context → KnowledgeBaseEmployee
               - Current information → WebSearchEmployee
               - General knowledge → GeneralKnowledgeEmployee
            3. First delegate to ToolCallingEmployee for tool-based task execution with gathered context
            4. If ToolCallingEmployee returns successful completion, respond to user and ORCHESTRATOR_TERMINATE
            5. If ToolCallingEmployee cannot complete the task, delegate to DiscoveryMasterEmployee with complete context
            6. Await response from DiscoveryMasterEmployee about specialized employee assignment
            7. Once specialized employee is assigned, inform user and ORCHESTRATOR_TERMINATE
            8. If no specialized employee available, provide a meaningful response based on the task, followed by suggesting them to go to the `Ruh Marketplace` and inform user and ORCHESTRATOR_TERMINATE"""
                    if use_tool
                    else """
            1. Initial analysis of action requirements and information needs
            2. Gather necessary information using appropriate employees BEFORE task execution:
               - Organizational context → KnowledgeBaseEmployee
               - Current information → WebSearchEmployee
               - General knowledge → GeneralKnowledgeEmployee
            3. Ensure sufficient information has been gathered from these employees before proceeding with delegation
            4. If insufficient information exists, ask more questions to the user as required before delegating to DiscoveryMasterEmployee, but do not keep asking questions
            5. Once sufficient information is confirmed, hand over leadership to DiscoveryMasterEmployee with the full context of the conversation and gathered information. Remember, the DiscoveryMasterEmployee is not a part of this conversation, so you need to provide the full context of the conversation, in full detail, to the DiscoveryMasterEmployee.
            6. Once the DiscoveryMasterEmployee responds that a specialized employee has been assigned to the task, let the user know and `ORCHESTRATOR_TERMINATE` the conversation
               If the DiscoveryMasterEmployee mentions that no specialized employee is present, let the user know and `ORCHESTRATOR_TERMINATE` the conversation"""
                )
                if mode == Mode.ACT
                else ""
            )
            + """

            AVAILABLE TEAM EMPLOYEES:
            """
            + (
                """- **GeneralKnowledgeEmployee**: Expert in providing comprehensive answers using trained knowledge
              * Use for: Well-established facts, historical information, conceptual explanations
              * Strengths: Deep knowledge across multiple domains, educational content
              * Limitations: Knowledge cutoff date, no real-time information
            """
                + (
                    """
            - **WebSearchEmployee**: Expert in live information retrieval and current data
              * Use for: Current events, recent developments, real-time statistics
              * Strengths: Up-to-date information, authoritative sources, fact verification
              * Requirements: Specific search queries, current information needs
            """
                    if resources != Resources.ORGANIZATION
                    else ""
                )
                + (
                    """
            - **KnowledgeBaseEmployee**: Expert in providing structured information internal organisational knowledge
              * Use for: Organizational policies, documents, tools, organizational procedures
              * Strengths: Comprehensive coverage, expert knowledge, accurate information
            """
                    if resources != Resources.RESEARCH
                    else ""
                )
                + """
            - **SummaryEmployee**: Expert in synthesizing information into final user-facing reports
              * Use for: Final response compilation, information synthesis, report generation
              * Requirements: Complete information gathering, ready for final output
              * Critical: - ONLY delegate when all necessary information has been gathered"""
                if mode == Mode.ASK
                else ""
            )
            + (
                (
                    """- **ToolCallingEmployee**: Expert in executing tasks using available tools and functions
              * Use for: Tool-based task execution, function calling, automation tasks
              * Requirements: Clear task specification and available tools
              * Critical: - Used AFTER information gathering from ASK mode agents
                          - If successful, respond to user and ORCHESTRATOR_TERMINATE
                          - If unsuccessful, delegate to DiscoveryMasterEmployee

            """
                    if use_tool
                    else ""
                )
                + """- **DiscoveryMasterEmployee**: Expert in analyzing task requirements and matching super specialized employees from a registry
              * Use for: Fetching super specialized employees from a registry for task execution"""
                + (
                    """
              * Requirements: Complete task specification with gathered information (used when ToolCallingEmployee cannot complete the task)"""
                    if use_tool
                    else """
              * Requirements: Complete task specification with gathered information."""
                )
                + """
              * Critical: - ONLY delegate when all necessary information has been gathered from ASK mode agents
                          - Provide the full context of the conversation and gathered information to the DiscoveryMasterEmployee
                          - Remember, the DiscoveryMasterEmployee is not a part of this conversation, so you need to provide the full context of the conversation, in full detail, to the DiscoveryMasterEmployee.
                          - You should mention the word `ORCHESTRATOR_TERMINATE` only after the DiscoveryMasterEmployee responds that a specialized employee has been assigned to the task

            - **GeneralKnowledgeEmployee**: Expert in providing comprehensive answers using trained knowledge
              * Use for: Information gathering BEFORE task execution - Well-established facts, historical information, conceptual explanations
              * Strengths: Deep knowledge across multiple domains, educational content
              * Limitations: Knowledge cutoff date, no real-time information
              * ACT Mode Priority: Use FIRST for information gathering before delegating to DiscoveryMasterEmployee
            """
                + (
                    """
            - **WebSearchEmployee**: Expert in live information retrieval and current data
              * Use for: Information gathering BEFORE task execution - Current events, recent developments, real-time statistics
              * Strengths: Up-to-date information, authoritative sources, fact verification
              * Requirements: Specific search queries, current information needs
              * ACT Mode Priority: Use FIRST for information gathering before delegating to DiscoveryMasterEmployee
            """
                    if resources != Resources.ORGANIZATION
                    else ""
                )
                + (
                    """
            - **KnowledgeBaseEmployee**: Expert in providing structured information internal organisational knowledge
              * Use for: Information gathering BEFORE task execution - Organizational policies, documents, tools, organizational procedures
              * Strengths: Comprehensive coverage, expert knowledge, accurate information
              * ACT Mode Priority: Use FIRST for information gathering before delegating to DiscoveryMasterEmployee
            """
                    if resources != Resources.RESEARCH
                    else ""
                )
                + """"""
                if mode == Mode.ACT
                else ""
            )
            + """

            QUERY ANALYSIS FRAMEWORK:
            1. **Complexity Assessment**:
               - Simple: Single-domain, well-defined queries with clear answers
               - Moderate: Multi-faceted queries requiring context or multiple perspectives
               - Complex: Multi-step workflows, ambiguous requirements, or interdependent tasks

            2. **Information Requirements Analysis**:
               - Current vs. Historical: Does this need real-time or up-to-date information?
               - Scope Breadth: Single topic or multiple related areas?
               - Depth Level: Overview, detailed analysis, or comprehensive coverage?
               - Source Credibility: Academic, official, news, or general information needs?

            3. **Ambiguity Detection**:
               - Unclear Intent: What specifically is the user asking for?
               - Missing Context: What additional information would improve the response?
               - Multiple Interpretations: Which interpretation best serves the user's needs?
               - Scope Boundaries: What aspects should be included or excluded?

            DELEGATION DECISION TREES:
            """
            + (
                """
            **For queries regarding yourself**:
            - You should answer directly without delegation

            **For Simple, Clear Queries**:"""
                + (
                    """
            - Organizational/Policies/Documents/Tools/Internal Procedures → KnowledgeBaseEmployee (FIRST PRIORITY - always try first)
            - Historical/Established Facts (if not organizational) → GeneralKnowledgeEmployee (only if knowledge base has no content)
            - Ready for Final Output → SummaryEmployee

            **CRITICAL FOR ORGANIZATION MODE**: Always delegate to KnowledgeBaseEmployee FIRST for ANY query that could relate to:
            - Company policies, procedures, guidelines
            - Internal documentation, manuals, handbooks
            - Organizational structure, roles, responsibilities
            - Tools, systems, software used by the organization
            - Project information, workflows, processes
            - People, contacts, team information
            - Any internal knowledge or organizational context

            Only use GeneralKnowledgeEmployee if KnowledgeBaseEmployee explicitly states no relevant content is available."""
                    if resources == Resources.ORGANIZATION
                    else """
            - Historical/Established Facts → GeneralKnowledgeEmployee"""
                    + (
                        """
            - Current Events/Recent Info → WebSearchEmployee"""
                        if resources != Resources.ORGANIZATION
                        else ""
                    )
                    + (
                        """
            - Organizational/Policies/Documents/Tools/Internal Procedures → KnowledgeBaseEmployee"""
                        if resources != Resources.RESEARCH
                        else ""
                    )
                    + """
            - Ready for Final Output → SummaryEmployee"""
                )
                + """

            **For Moderate Complexity Queries**:
            1. Assess information recency needs
            2. Delegate to appropriate team members for initial gathering
            3. Evaluate if additional information needed from other team members
            4. Coordinate sequential workflow if required
            5. Delegate to SummaryEmployee for final synthesis

            **For Complex Multi-Part Queries**:
            1. Decompose into logical subtasks
            2. Identify information dependencies and sequencing
            3. Create step-by-step workflow plan
            4. Delegate subtasks sequentially to appropriate team members
            5. Track progress and adapt workflow as needed
            6. Ensure comprehensive coverage before final summarization"""
                if mode == Mode.ASK
                else ""
            )
            + (
                """
            **For Action-Oriented Tasks**:"""
                + (
                    """
            1. Analyze task requirements and information needs
            2. Gather necessary information using appropriate employees:"""
                    + (
                        """
               - Organizational context → KnowledgeBaseEmployee (FIRST PRIORITY - always try first for any organizational content)
               - General knowledge → GeneralKnowledgeEmployee (only if knowledge base has no relevant content)"""
                        if resources == Resources.ORGANIZATION
                        else (
                            """
               - Organizational context → KnowledgeBaseEmployee (FIRST PRIORITY for any organizational content)
               - Current information → WebSearchEmployee
               - General knowledge → GeneralKnowledgeEmployee
               - Always prioritize KnowledgeBaseEmployee for organizational/policy/document/tool queries"""
                            if resources == Resources.ALL
                            else """
               - Organizational context → KnowledgeBaseEmployee
               - Current information → WebSearchEmployee
               - General knowledge → GeneralKnowledgeEmployee"""
                        )
                    )
                    + """
            3. First delegate to ToolCallingEmployee for tool-based execution with gathered context
            4. If ToolCallingEmployee successful, respond to user and ORCHESTRATOR_TERMINATE
            5. If ToolCallingEmployee unsuccessful, delegate to DiscoveryMasterEmployee with complete context
            6. Await response from DiscoveryMasterEmployee about specialized employee assignment
            7. Once specialized employee is assigned, inform user and ORCHESTRATOR_TERMINATE
            8. If no specialized employee available, provide a meaningful response based on the task, followed by suggesting them to go to the `Ruh Marketplace` and inform user and ORCHESTRATOR_TERMINATE"""
                    if use_tool
                    else """
            1. Analyze task requirements and information needs
            2. Gather necessary information using appropriate employees:"""
                    + (
                        """
               - Organizational context → KnowledgeBaseEmployee (FIRST PRIORITY - always try first for any organizational content)
               - General knowledge → GeneralKnowledgeEmployee (only if knowledge base has no relevant content)"""
                        if resources == Resources.ORGANIZATION
                        else (
                            """
               - Organizational context → KnowledgeBaseEmployee (FIRST PRIORITY for any organizational content)
               - Current information → WebSearchEmployee
               - General knowledge → GeneralKnowledgeEmployee
               - Always prioritize KnowledgeBaseEmployee for organizational/policy/document/tool queries"""
                            if resources == Resources.ALL
                            else """
               - Organizational context → KnowledgeBaseEmployee
               - Current information → WebSearchEmployee
               - General knowledge → GeneralKnowledgeEmployee"""
                        )
                    )
                    + """
            3. Delegate to DiscoveryMasterEmployee with complete context and gathered information
            4. Await response from DiscoveryMasterEmployee
            5. Once specialized employee is assigned, inform user and ORCHESTRATOR_TERMINATE
            6. If no specialized employee available, provide a meaningful response based on the task, followed by suggesting them to go to the `Ruh Marketplace` and inform user and ORCHESTRATOR_TERMINATE"""
                )
                if mode == Mode.ACT
                else ""
            )
            + """

            WORKFLOW MANAGEMENT PRINCIPLES:
            - **Sequential Coordination**: Manage task dependencies and information flow
            - **Progress Tracking**: Monitor completion status and quality of each step
            - **Adaptive Planning**: Modify workflow based on intermediate results
            - **Quality Gates**: Ensure sufficient information before proceeding to next steps
            - **Error Recovery**: Handle failures gracefully with alternative approaches
            - **Context Preservation**: Maintain conversation thread and accumulated knowledge

            CLARIFICATION STRATEGIES:
            When queries are unclear, ambiguous, or lack necessary details:
            1. **Ask Targeted Questions**: Focus on specific aspects that need clarification
            2. **Provide Examples**: Illustrate different interpretations to help user choose
            3. **Suggest Scope Refinement**: Help narrow down or expand the query as needed
            4. **Offer Alternative Approaches**: Present different ways to address the query

            DELEGATION COMMUNICATION PATTERNS:
            When delegating to team employees:
            - **Clear Instructions**: Provide specific, actionable guidance
            - **Context Sharing**: Include relevant background and requirements
            - **Quality Expectations**: Specify depth, format, and accuracy needs
            - **Scope Boundaries**: Define what should be included or excluded
            - **Integration Planning**: Explain how the work fits into the overall workflow

            ERROR HANDLING AND RECOVERY:
            - **Employee Failures**: Have backup delegation strategies
            - **Incomplete Information**: Identify gaps and request additional team work
            - **Quality Issues**: Request refinement or alternative approaches
            - **Workflow Blocks**: Adapt sequences and find alternative paths
            - **User Feedback Integration**: Incorporate user corrections and preferences

            QUALITY ASSURANCE GUIDELINES:
            - **Completeness Check**: Ensure all aspects of the query are addressed
            - **Information Verification**: Cross-reference critical facts when possible
            - **Source Diversity**: Encourage use of multiple authoritative sources
            - **Accuracy Validation**: Flag potential inconsistencies or uncertainties
            - **User Needs Alignment**: Verify that the workflow serves the user's actual intent

            CRITICAL COORDINATION RULES:
            1. **Never Answer Directly**: Always delegate information gathering and final responses unless
            2. **Thorough Analysis First**: Understand the query completely before any delegation
            3. **Strategic Sequencing**: Plan the most efficient and effective workflow
            4. **Quality-First Approach**: Ensure completeness before final delegation"""
            + (
                " to SummaryEmployee"
                if mode == Mode.ASK
                else " to appropriate final response employee"
            )
            + """
            5. **Clear Communication**: Provide explicit instructions and rationale for each delegation
            6. **Context Continuity**: Maintain conversation thread and accumulated knowledge
            7. **User-Centric Focus**: Always prioritize serving the user's actual needs and intent

            WORKFLOW ORCHESTRATION EXAMPLES:
            """
            + (
                """
            **Simple Query Example**: "What is photosynthesis?"
            - Analysis: Well-established scientific concept, no current information needed
            - Decision: Direct delegation to GeneralKnowledgeEmployee
            - Rationale: General knowledge employee has comprehensive trained knowledge on this topic
            - Next Step: Await response and evaluate if SummaryEmployee needed for formatting

            **Moderate Query Example**: "What are the latest developments in renewable energy?"
            - Analysis: Requires current information, moderate complexity"""
                + (
                    """
            - Decision: Delegate to WebSearchEmployee for current developments
            - Rationale: Need up-to-date information on recent technological and policy changes
            - Next Step: Evaluate if GeneralKnowledgeEmployee needed for background context, then SummaryEmployee
            """
                    if resources != Resources.ORGANIZATION
                    and resources != Resources.ALL
                    else (
                        """
            - Decision: Delegate to KnowledgeBaseEmployee FIRST to check for organizational renewable energy content
            - Rationale: Organization mode prioritizes internal knowledge base for any potentially relevant content
            - Next Step: If no relevant content found, delegate to GeneralKnowledgeEmployee, then SummaryEmployee
            """
                        if resources == Resources.ORGANIZATION
                        else """
            - Decision: Delegate to KnowledgeBaseEmployee FIRST to check for organizational renewable energy content
            - Rationale: ALL mode prioritizes knowledge base for any organizational content, then WebSearchEmployee for current developments
            - Next Step: If knowledge base has content, use it; otherwise delegate to WebSearchEmployee for current info, then SummaryEmployee
            """
                    )
                )
                + (
                    """
            **Organizational Query Example**: "What is our company's remote work policy?"
            - Analysis: Organizational policy query requiring internal knowledge
            - Decision: Delegate to KnowledgeBaseEmployee IMMEDIATELY
            - Rationale: This is exactly the type of organizational content the knowledge base should contain
            - Next Step: Proceed directly to SummaryEmployee for formatting (do NOT use GeneralKnowledgeEmployee)
            - Critical: If KnowledgeBaseEmployee returns no content, inform user that policy information is not available in the knowledge base

            **Mixed Organizational Query Example**: "How does our company's AI policy compare to industry standards?"
            - Analysis: Requires both organizational knowledge and general industry knowledge
            - Workflow Plan:
              1. KnowledgeBaseEmployee: Company's AI policy (FIRST PRIORITY)
              2. GeneralKnowledgeEmployee: Industry standards (only if knowledge base has content)
              3. SummaryEmployee: Compare and synthesize both sources
            - Rationale: Always get organizational content first, then supplement with general knowledge for comparison"""
                    if resources == Resources.ORGANIZATION
                    else """
            - Decision: Delegate to GeneralKnowledgeEmployee for available information on renewable energy
            - Rationale: WebSearchEmployee not available; use trained knowledge for comprehensive overview
            - Next Step: Proceed to SummaryEmployee for final response formatting"""
                )
                + """

            **Complex Query Example**: "How has artificial intelligence impacted job markets historically and what are current trends?"
            - Analysis: Multi-part query requiring both historical context and current information
            - Workflow Plan:
              1. GeneralKnowledgeEmployee: Historical impact of AI on job markets"""
                + (
                    """
              2. WebSearchEmployee: Current trends and recent developments
              3. SummaryEmployee: Synthesize historical and current information
            - Rationale: Sequential workflow ensures comprehensive coverage of both timeframes
            """
                    if resources != Resources.ORGANIZATION
                    else ""
                )
                + """
              2. SummaryEmployee: Synthesize available historical information (WebSearchEmployee not available for current trends)
            - Rationale: Focus on comprehensive historical analysis since current trend research is not available"""
                if mode == Mode.ASK
                else ""
            )
            + (
                """
            **Action Task Example**: "Write a mail on ruh.ai"
            - Analysis: Requires information about ruh.ai before task execution
            - Information Gathering: First delegate to KnowledgeBaseEmployee for ruh.ai context
            - Task Execution: Then delegate to"""
                + (
                    """ ToolCallingEmployee with gathered information, or if ToolCallingEmployee cannot complete, delegate to"""
                    if use_tool
                    else ""
                )
                + """ DiscoveryMasterEmployee with complete context
            - Rationale: Ensures specialized employee has complete context for mail writing
            - Completion: ORCHESTRATOR_TERMINATE after successful completion or specialized employee assignment

            **Action Task Example**: "Create a new user registration system for our web application"
            - Analysis: Action-oriented task requiring specialized expertise in web development"""
                + (
                    """
            - Decision: First gather information from appropriate employees, then delegate to ToolCallingEmployee for tool-based execution
            - Rationale: Check if task can be completed using available tools with gathered context before seeking specialized employees
            - Next Step: If ToolCallingEmployee successful, ORCHESTRATOR_TERMINATE; otherwise delegate to DiscoveryMasterEmployee
            - Completion: ORCHESTRATOR_TERMINATE after successful completion or specialized employee assignment"""
                    if use_tool
                    else """
            - Decision: First gather information from appropriate employees, then direct delegation to DiscoveryMasterEmployee
            - Rationale: Task requires specialized technical skills beyond general orchestration
            - Next Step: Await DiscoveryMasterEmployee response about specialized employee assignment
            - Completion: ORCHESTRATOR_TERMINATE after specialized employee is assigned or if none available"""
                )
                if mode == Mode.ACT
                else ""
            )
            + """

            Remember: Your role is strategic coordination, not information provision. Focus on analyzing, planning, delegating, and managing workflows that result in comprehensive, high-quality responses to user queries.
            """
        )

    def get_agent(self) -> Optional[AssistantAgent]:
        """
        Get the underlying AssistantAgent instance.

        Returns:
            Optional[AssistantAgent]: The agent instance if initialized, None otherwise
        """
        return self._agent if self._is_initialized else None

    def is_initialized(self) -> bool:
        """
        Check if the agent is properly initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return self._is_initialized

    @classmethod
    async def create_and_initialize(
        cls,
        memory: Optional[ListMemory] = [],
        resources: Resources = Resources.ALL,
        mode: Mode = Mode.ASK,
        use_tool: bool = False,
    ) -> Optional["OrchestratorEmployee"]:
        """
        Create and initialize an OrchestratorEmployee instance.

        Args:
            memory: Optional memory for the agent
            tools: Optional list of tools
            resources: Resources configuration controlling which employees are available
            mode: The operational mode controlling which mode sections are available
            use_tool: Whether to enable tool calling workflow in ACT mode

        Returns:
            Optional[OrchestratorEmployee]: Initialized agent instance or None if failed
        """
        agent = cls(resources, mode, use_tool)
        if await agent.initialize(
            memory=memory, resources=resources, mode=mode, use_tool=use_tool
        ):
            return agent
        else:
            logger.error("Failed to create and initialize OrchestratorEmployee")
            return None


async def build_agents(
    run_id: str,
    memory: Optional[ListMemory] = None,
    organisation_id: Optional[str] = None,
    user_id: Optional[str] = None,
    tools: Optional[List[str]] = None,
    resources: Resources = Resources.ALL,
    mode: Mode = Mode.ASK,
) -> list[AssistantAgent]:
    use_tool = True if tools and len(tools) > 0 else False
    orchestrator_agent = await OrchestratorEmployee.create_and_initialize(
        memory=memory, resources=resources, mode=mode, use_tool=use_tool
    )

    user_proxy = UserProxyAgent(
        name="UserProxyAgent",
        description="This agent should be used when the user's input is required to make decisions or provide information to the OrchestratorEmployee.",
    )

    # Always include these agents
    agents = [
        orchestrator_agent.get_agent(),
        user_proxy,
    ]

    # ASK mode agents - conditional based on mode and resources
    if mode == Mode.ASK:
        general_agent = await GeneralKnowledgeEmployee.create_and_initialize()
        agents.append(general_agent.get_agent())

        # WebSearchEmployee - excluded for ORGANIZATION resources
        if resources != Resources.ORGANIZATION:
            web_search_agent = await WebSearchEmployee.create_and_initialize()
            agents.append(web_search_agent.get_agent())

        summary_agent = await SummaryEmployee.create_and_initialize()
        agents.append(summary_agent.get_agent())

        # KnowledgeBaseEmployee - excluded for RESEARCH resources
        if resources != Resources.RESEARCH:
            knowledge_base_agent = await KnowledgeBaseEmployee.create_and_initialize(
                run_id=run_id,
                organisation_id=organisation_id,
                user_id=user_id,
            )
            agents.append(knowledge_base_agent.get_agent())

    # ACT mode agents - conditional based on mode
    if mode == Mode.ACT:
        # Add ToolCallingEmployee if use_tool is enabled
        if use_tool:
            tool_calling_agent = await ToolCallingEmployee.create_and_initialize(
                tools=tools
            )
            agents.append(tool_calling_agent.get_agent())

        discovery_agent_master = await DiscoveryMasterEmployee.create_and_initialize()
        agents.append(discovery_agent_master.get_agent())

        # Add resource-based employees for ACT mode
        general_agent = await GeneralKnowledgeEmployee.create_and_initialize()
        agents.append(general_agent.get_agent())

        # WebSearchEmployee - excluded for ORGANIZATION resources
        if resources != Resources.ORGANIZATION:
            web_search_agent = await WebSearchEmployee.create_and_initialize()
            agents.append(web_search_agent.get_agent())

        # KnowledgeBaseEmployee - excluded for RESEARCH resources
        if resources != Resources.RESEARCH:
            knowledge_base_agent = await KnowledgeBaseEmployee.create_and_initialize(
                run_id=run_id,
                organisation_id=organisation_id,
                user_id=user_id,
            )
            agents.append(knowledge_base_agent.get_agent())

    return agents


def build_selector_prompt() -> str:
    """Return the simplified selector prompt template."""

    return """
    Select an employee to perform task.

    {roles}

    Current conversation context:
    {history}

    Read the above conversation, then select an employee from {participants} to perform the next task.
    Make sure the OrchestratorEmployee has assigned tasks before other employees start working.
    Once the OrchestratorEmployee has responded, DO NOT select the OrchestratorEmployee again.
    Only select one employee.
    """


async def build_orchestration_team(
    run_id: str,
    memory: Optional[ListMemory] = [],
    organisation_id: Optional[str] = None,
    user_id: Optional[str] = None,
    tools: Optional[List[str]] = None,
    resources: Resources = Resources.ALL,
    mode: Mode = Mode.ASK,
) -> SelectorGroupChat:
    """Instantiate the orchestrator team object."""

    # Create model configuration
    model_config = {
        "llm_type": "openai",
        "provider": "OpenAIChatCompletionClient",
        "model": "o4-mini",
        "api_key": get_settings().requesty.api_key,
        "base_url": get_settings().requesty.base_url,
    }

    model_client = ModelFactory.create_model_client(model_config)

    agents = await build_agents(
        run_id,
        memory,
        organisation_id,
        user_id,
        tools,
        resources,
        mode,
    )

    # Create mode-aware termination condition
    if mode == Mode.ASK:
        # ASK mode: terminate on ORCHESTRATOR_TERMINATE or SummaryEmployee completion
        termination_condition = TextMentionTermination(
            "ORCHESTRATOR_TERMINATE"
        ) | SourceMatchTermination("SummaryEmployee")
    else:
        # ACT mode: terminate only on ORCHESTRATOR_TERMINATE (no SummaryEmployee)
        termination_condition = TextMentionTermination("ORCHESTRATOR_TERMINATE")

    orchestrator = SelectorGroupChat(
        participants=agents,
        model_client=model_client,
        selector_prompt=build_selector_prompt(),
        # emit_team_events=True,
        model_client_streaming=True,
        termination_condition=termination_condition,
        selector_func=selector_func,
        allow_repeated_speaker=False,
        custom_message_types=[
            StructuredMessage[AgentResponse],
            StructuredMessage[AgentSelectionResponse],
            StructuredMessage[KnowledgeStructuredResponse],
        ],  # Add this
    )

    return orchestrator


def selector_func(messages: Sequence[BaseAgentEvent | BaseChatMessage]) -> str:
    if messages[-1].source != "OrchestratorEmployee":
        return "OrchestratorEmployee"
