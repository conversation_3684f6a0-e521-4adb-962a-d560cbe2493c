"""
Mem0 Memory Utilities for AutoGen Agent Platform

This module provides centralized utilities for:
1. Creating and configuring Mem0Memory instances
2. Storing messages in Mem0 memory with proper error handling
3. Managing memory lifecycle across different agent types
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List

from autogen_agentchat.agents import AssistantAgent

# Add Mem0 import with fallback
try:
    from autogen_ext.memory.mem0 import Mem0Memory
    MEM0_AVAILABLE = True
except ImportError:
    MEM0_AVAILABLE = False
    logging.warning("Mem0Memory not available. Install autogen-ext with mem0 support.")

# # Add SentenceTransformer import with fallback
# try:
#     from sentence_transformers import SentenceTransformer
#     SENTENCE_TRANSFORMER_AVAILABLE = True
# except ImportError:
#     SENTENCE_TRANSFORMER_AVAILABLE = False
#     logging.warning("SentenceTransformer not available. Install sentence-transformers package.")

from ..shared.config.base import get_settings

logger = logging.getLogger(__name__)


class Mem0Utils:
    """Centralized utilities for Mem0 memory operations"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = logger
        
        # # Initialize SentenceTransformer model
        # if not SENTENCE_TRANSFORMER_AVAILABLE:
        #     raise ImportError("SentenceTransformer is required. Install sentence-transformers package.")
        
        # try:
        #     self._embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        #     self.logger.info("Initialized SentenceTransformer model: all-MiniLM-L6-v2")
        # except Exception as e:
        #     self.logger.error(f"Failed to initialize SentenceTransformer model: {e}")
        #     raise
    
    def create_mem0_memory(
        self,
        user_id: str,
        agent_id: Optional[str] = None,
        session_id: Optional[str] = None,
        organization_id: Optional[str] = None,
        collection_name: str = "agent-memories",
    ) -> Optional[object]:
        """
        Create and configure a Mem0Memory instance with Pinecone configuration.
        
        Args:
            user_id: User identifier for memory isolation
            agent_id: Agent identifier for memory context
            session_id: Session identifier for memory context
            organization_id: Organization identifier for multi-tenant support
            collection_name: Pinecone collection name
            
        Returns:
            Configured Mem0Memory instance or None if not available
        """
        if not MEM0_AVAILABLE:
            self.logger.debug("Mem0Memory not available, skipping memory creation")
            return None
            
        try:
            # Use SentenceTransformer configuration directly
            # embedding_dims = 384  # all-MiniLM-L6-v2 dimension
            # embedder_config = {
            #     "provider": "huggingface",
            #     "config": {
            #         "model": "all-MiniLM-L6-v2",  # CORRECT: this is the model name string
            #         "embedding_dims": embedding_dims
            #     }
            # }
            embedding_dims = self.settings.pinecone.dimension
            embedder_config = {
                                "provider": "openai",
                                "config": {
                                    "model": "text-embedding-3-small",
                                    "api_key": self.settings.openai.api_key,
                                    "embedding_dims": self.settings.pinecone.dimension
                                }
                            }

            
            # Create Pinecone configuration with embedder
            # db_config = {
            #     "vector_store": {
            #         "provider": "pinecone",
            #         "config": {
            #             "collection_name": collection_name,
            #             "embedding_model_dims": embedding_dims,
            #             "serverless_config": {
            #                 "cloud": self.settings.pinecone.cloud,
            #                 "region": self.settings.pinecone.environment,
            #             },
            #             "metric": self.settings.pinecone.metric,
            #             "api_key": self.settings.pinecone.api_key,
            #         }
            #     },
            #     "embedder": embedder_config
            # }


            # Create Qdrant configuration with embedder
            db_config = {
                "vector_store": {
                    "provider": "qdrant",
                    "config": {
                        "collection_name": collection_name,
                        "embedding_model_dims": embedding_dims,
                        "host": "localhost",
                        "port": 6333,
                    }
                },
                "embedder": embedder_config
            }
            
            # Create Mem0Memory instance with just user_id
            mem0_memory = Mem0Memory(
                user_id=user_id,
                is_cloud=False,  # Use local configuration
                config=db_config,
            )
            
            self.logger.info(
                f"Created Mem0Memory for user: {user_id}, session: {session_id}, org: {organization_id}"
            )
            
            return mem0_memory
            
        except Exception as e:
            self.logger.error(f"Failed to create Mem0Memory: {e}")
            return None
    
    async def store_message_in_mem0(
        self,
        role: str,
        content: str,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        session_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        agent: Optional[AssistantAgent] = None,
        source: Optional[str] = None,
        team_conversation_id: Optional[str] = None,
    ) -> bool:
        """
        Store a message in Mem0 memory if available.
        
        Args:
            role: Message role ('user', 'assistant', etc.)
            content: Message content
            user_id: User identifier
            agent_id: Agent identifier
            session_id: Session identifier
            metadata: Optional metadata for the message
            agent: Optional agent instance (if available, will use agent's existing memory)
            source: Optional source identifier (for orchestration messages)
            team_conversation_id: Optional team conversation identifier
            
        Returns:
            True if stored successfully, False otherwise
        """
        if not MEM0_AVAILABLE:
            return False
            
        async def _background_store():
            """Background task to store message in Mem0"""
            try:
                mem0_memory = None
                
                # First, try to get Mem0Memory from agent if provided
                if agent:
                    # Check if agent has memory attribute using getattr with default
                    agent_memory_list = getattr(agent, '_memory', None)
                    if not agent_memory_list:
                        # Try alternative memory attribute names
                        agent_memory_list = getattr(agent, 'memory', None)
                    
                    if agent_memory_list:
                        # Find Mem0Memory instance in the memory list
                        for memory in agent_memory_list:
                            # Check if this is a Mem0Memory instance
                            if hasattr(memory, 'add') and 'Mem0Memory' in str(type(memory)):
                                mem0_memory = memory
                                break
                
                # If no memory found in agent or no agent provided, create a new Mem0Memory instance
                if not mem0_memory:
                    # Use the proper create_mem0_memory method with full Pinecone configuration
                    mem0_memory = self.create_mem0_memory(
                        user_id=user_id or "unknown",
                        agent_id=agent_id,
                        session_id=session_id,
                        organization_id=metadata.get("organization_id") if metadata else None,
                    )
                
                # Format message for storage with timestamp
                timestamp = datetime.now().isoformat()
                
                # Include source if provided (for orchestration messages)
                if source and source != role:
                    formatted_message = f"[{timestamp}] {role} ({source}): {content}"
                else:
                    formatted_message = f"[{timestamp}] {role}: {content}"
                
                # Add metadata if provided
                if metadata:
                    formatted_message += f" | Metadata: {metadata}"
                
                # Store in Mem0 memory using the correct API
                await mem0_memory.add(formatted_message)
                
                context_info = f"user: {user_id}, agent: {agent_id}, session: {session_id}"
                if team_conversation_id:
                    context_info += f", team: {team_conversation_id}"
                
                self.logger.info(f"Stored {role} message in Mem0 memory for {context_info}")
                
                return True
                
            except Exception as e:
                self.logger.error(f"Failed to store message in Mem0 memory: {e}")
                return False
        
        # Create background task and don't await it
        task = asyncio.create_task(_background_store())
        return True  # Return True immediately since we're doing background storage
    

    
    def is_mem0_available(self) -> bool:
        """Check if Mem0 is available"""
        return MEM0_AVAILABLE
    
    async def retrieve_memories(
        self,
        user_id: str,
        query: Optional[str] = None,
        agent_id: Optional[str] = None,
        session_id: Optional[str] = None,
        limit: int = 10,
    ) -> List[Dict[str, Any]]:
        """
        Retrieve memories from Mem0.
        
        Args:
            user_id: User identifier
            query: Optional query to search for specific memories
            agent_id: Optional agent identifier for filtering
            session_id: Optional session identifier for filtering
            limit: Maximum number of memories to retrieve
            
        Returns:
            List of memory dictionaries
        """
        if not MEM0_AVAILABLE:
            return []
            
        try:
            # Use the proper create_mem0_memory method with full Pinecone configuration
            mem0_memory = self.create_mem0_memory(
                user_id=user_id,
                agent_id=agent_id,
                session_id=session_id,
            )
            
            if query:
                memories = await mem0_memory.search(query, limit=limit)
            else:
                memories = await mem0_memory.get_all(limit=limit)
            
            self.logger.info(f"Retrieved {len(memories)} memories for user: {user_id}")
            return memories
            
        except Exception as e:
            self.logger.error(f"Failed to retrieve memories: {e}")
            return []


# Create global instance
mem0_utils = Mem0Utils() 