import asyncio
import json
import logging
import time
from contextlib import asynccontextmanager
from dataclasses import dataclass
from typing import Any, AsyncGenerator, Dict, List, Optional, Union
from datetime import datetime

from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.messages import (
    MultiModalMessage,
    StructuredMessage,
    TextMessage,
)
from autogen_core import CancellationToken
from autogen_core.memory import MemoryContent, MemoryMimeType
from pydantic import BaseModel



from ..helper.session_manager import SessionManager
from ..schemas.chat import AgentResponse, MessageType
from ..schemas.kafka import MessageAttachment
from ..utils.file_processor import FileProcessor
from .agent_factory import AgentFactory
from .mem0_utils import mem0_utils
from .message_processor import MessageProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@dataclass
class ChatSession:
    """Data class for chat session tracking"""

    session_id: str
    start_time: float
    last_activity: float

    def is_expired(self, timeout: float) -> bool:
        """Check if session is expired"""
        return time.time() - self.start_time > timeout

    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = time.time()


class ChatProcessor:
    """Optimized chat processor with improved session management and error handling"""

    def __init__(self, session_manager: SessionManager, agent_factory: AgentFactory):
        self.session_manager = session_manager
        self.agent_factory = agent_factory
        self.logger = logger
        self.message_processor = MessageProcessor()
        self.file_processor = FileProcessor()

        # Session tracking with improved data structure
        self._active_sessions: Dict[str, ChatSession] = {}
        self._session_lock = asyncio.Lock()
        self._chat_timeout = 300  # 5 minutes timeout

        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._periodic_cleanup())



    async def _periodic_cleanup(self):
        """Periodic cleanup of expired sessions"""
        while True:
            try:
                await asyncio.sleep(60)  # Run every minute
                await self._cleanup_expired_sessions()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in periodic cleanup: {e}")

    async def _cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        expired_sessions = []

        async with self._session_lock:
            for session_id, session in list(self._active_sessions.items()):
                if session.is_expired(self._chat_timeout):
                    expired_sessions.append(session_id)

            for session_id in expired_sessions:
                del self._active_sessions[session_id]
                self.logger.warning(f"Cleaned up expired session: {session_id}")

    @asynccontextmanager
    async def _session_context(self, session_id: str):
        """Context manager for session tracking"""
        session = ChatSession(
            session_id=session_id, start_time=time.time(), last_activity=time.time()
        )

        async with self._session_lock:
            self._active_sessions[session_id] = session

        try:
            yield session
        finally:
            async with self._session_lock:
                self._active_sessions.pop(session_id, None)

    async def _is_session_active(self, session_id: str) -> bool:
        """Check if session is active"""
        async with self._session_lock:
            return session_id in self._active_sessions

    def _create_user_input_patch(self, user_message: str):
        """Create patched user input function for autogen v0.4"""

        async def _patched_user_input(prompt, cancellation_token=None):
            """
            Patched input function that returns the user message instead of
            prompting for input. This prevents EOF errors in server environments.
            """
            self.logger.debug(f"UserProxy input patched, returning: {user_message}")
            return user_message

        return _patched_user_input

    def _patch_user_proxy_agents(self, obj, user_message: str):
        """
        Recursively patch UserProxyAgent input function for autogen v0.4

        In autogen v0.4, UserProxyAgent has different internal structure.
        We need to patch both 'input' and '_get_input' methods to handle
        different scenarios.
        """
        patched_input = self._create_user_input_patch(user_message)

        if isinstance(obj, UserProxyAgent):
            # Patch the main input method
            obj.input = patched_input

            # Also patch the internal _get_input method if it exists
            if hasattr(obj, "_get_input"):
                obj._get_input = patched_input

            # Set human_input_mode to NEVER to prevent input prompts
            if hasattr(obj, "human_input_mode"):
                obj.human_input_mode = "NEVER"

            self.logger.debug(
                f"Patched UserProxyAgent: {getattr(obj, 'name', 'unknown')}"
            )

        elif hasattr(obj, "participants"):
            # Handle team objects with participants
            for participant in getattr(obj, "participants", []):
                self._patch_user_proxy_agents(participant, user_message)

        elif hasattr(obj, "_participants"):
            # Handle some team objects that use _participants
            for participant in getattr(obj, "_participants", []):
                self._patch_user_proxy_agents(participant, user_message)

    def create_safe_user_proxy_agent(
        self, name: str = "user_proxy", user_message: str = ""
    ) -> UserProxyAgent:
        """
        Create a UserProxyAgent configured to prevent EOF errors in server
        environments.

        This method creates a UserProxyAgent with proper input handling that
        won't try to read from stdin, preventing EOF errors when running in
        non-interactive environments like servers or APIs.
        """
        try:
            # Create UserProxyAgent with safe defaults
            user_proxy = UserProxyAgent(name=name)

            # Patch the input method immediately
            user_proxy.input = self._create_user_input_patch(user_message)

            # Set human_input_mode to NEVER to prevent input prompts
            if hasattr(user_proxy, "human_input_mode"):
                user_proxy.human_input_mode = "NEVER"

            # Also patch the internal _get_input method if it exists
            if hasattr(user_proxy, "_get_input"):
                user_proxy._get_input = self._create_user_input_patch(user_message)

            self.logger.debug(f"Created safe UserProxyAgent: {name}")
            return user_proxy

        except Exception as e:
            self.logger.error(f"Failed to create safe UserProxyAgent: {e}")
            raise

    async def _process_message_response(
        self, response: Any, agent: AssistantAgent
    ) -> Optional[AgentResponse]:
        """Process a single message response"""

        print(f"Processing response: {response}")

        msg_type: MessageType = self.message_processor.get_message_type(response)

        source, content, metadata = self.message_processor.extract_message_content(
            response
        )

        return AgentResponse(
            content=content,
            source=source,
            models_usage=self.message_processor.extract_models_usage(response),
            message_type=msg_type.value,
            metadata=metadata,
        )

    async def _update_session_memory(
        self, session_id: str, role: str, content: Union[str, List, Dict]
    ):
        """Update session memory with error handling"""
        try:
            await self.session_manager.update_session_memory(
                session_id=session_id, message={"role": role, "content": content}
            )
        except Exception as e:
            self.logger.error(f"Failed to update session memory for {session_id}: {e}")

    async def _create_message_from_content(
        self, user_message: str, attachments: Optional[List[MessageAttachment]] = None
    ) -> Union[TextMessage, MultiModalMessage]:
        """
        Create appropriate message type based on content and attachments

        Args:
            user_message: Text content of the message
            attachments: Optional list of file attachments

        Returns:
            TextMessage if no attachments, MultiModalMessage if attachments present
        """
        if not attachments:
            return TextMessage(content=user_message, source="user")

        # Validate attachments first
        validation_errors = self.file_processor.validate_attachments(attachments)
        if validation_errors:
            error_msg = "Attachment validation failed: " + "; ".join(validation_errors)
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # Create multimodal message with attachments
        try:
            multi_modal_message = await self.file_processor.create_multimodal_message(
                user_message, attachments, source="user"
            )

            attachment_summary = self.file_processor.get_attachment_summary(attachments)

            self.logger.info(f"Created multimodal message with {attachment_summary}")

            return multi_modal_message

        except Exception as e:
            self.logger.error(f"Failed to create multimodal message: {e}")
            raise ValueError(f"Failed to process attachments: {e}")

    async def process_chat(
        self,
        session_id: str,
        user_message: Union[str, Dict, List],
        agent: AssistantAgent,
        run_id: Optional[str] = None,
        cancellation_token: Optional[CancellationToken] = None,
        attachments: Optional[List[MessageAttachment]] = None,
    ) -> AsyncGenerator[Dict, None]:
        """
        Process chat with optimized streaming and error handling
        """
        self.logger.info(f"Starting chat processing for session: {session_id}")

        messages_to_send = []
        processed_message = None

        # Get session data to extract user_id and agent_id for Mem0 storage
        try:
            session_data = await self.session_manager.get_session_data(session_id)
            # Better approach - if session data is a dict
            user_id = session_data.get("user_id")
            agent_config = session_data.get("agent_config", {})
            agent_id = agent_config.get("id")
        except Exception as e:
            self.logger.warning(f"Could not extract session data for Mem0 storage: {e}")
            user_id = None
            agent_id = None

        async with self._session_context(session_id) as session:
            try:
                # Update session with user message
                await self._update_session_memory(session_id, "user", user_message)

                # Store user message in Mem0 memory
                if isinstance(user_message, str):
                    await mem0_utils.store_message_in_mem0(
                        role="user",
                        content=user_message,
                        user_id=user_id,
                        agent_id=agent_id,
                        session_id=session_id,
                        agent=agent,
                    )

                if isinstance(user_message, str):
                    processed_message = user_message

                # Create the main user message based on type and content
                if isinstance(user_message, dict):
                    # Handle dictionary input (structured content)
                    if "content" in user_message:
                        try:

                            class DynamicContent(BaseModel):
                                content: str = user_message.get("content", "")
                                data: Dict = user_message

                            structured_content = DynamicContent(
                                content=user_message.get("content", ""),
                                data=user_message,
                            )

                            main_message = StructuredMessage(
                                content=structured_content, source="user"
                            )
                        except Exception as e:
                            self.logger.warning(
                                f"Failed to create structured message from dict: {e}"
                            )
                            # Fallback to text message
                            main_message = TextMessage(
                                content=str(
                                    user_message.get(
                                        "content", json.dumps(user_message)
                                    )
                                ),
                                source="user",
                            )
                    else:
                        # Convert dict to text message
                        content = user_message.get("content", json.dumps(user_message))
                        main_message = TextMessage(content=content, source="user")

                    messages_to_send.append(main_message)

                elif isinstance(user_message, list):
                    # Handle list input (multiple content items)
                    for content in user_message:
                        if isinstance(content, str):
                            message = TextMessage(content=content, source="user")
                            messages_to_send.append(message)
                        if isinstance(content, dict):
                            message = TextMessage(
                                content=json.dumps(content), source="user"
                            )
                            messages_to_send.append(message)

                else:
                    # Handle string input (most common case)
                    main_message = await self._create_message_from_content(
                        str(processed_message), attachments
                    )
                    messages_to_send.append(main_message)

                # Track assistant responses for Mem0 storage
                assistant_response_content = ""
                chunk_id = 0
                async for response in agent.run_stream(
                    task=messages_to_send,
                    cancellation_token=cancellation_token,
                    output_task_messages=False,
                ):
                    session.update_activity()

                    # Process response
                    agent_response = await self._process_message_response(
                        response, agent
                    )

                    if agent_response and agent_response.message_type != "unknown":

                        if agent_response.message_type == "text":
                            # Update session memory
                            await self._update_session_memory(
                                session_id,
                                agent_response.source,
                                agent_response.content,
                            )

                            # Accumulate assistant response content for Mem0 storage
                            if agent_response.source == "assistant" or agent_response.source.endswith("_agent"):
                                assistant_response_content += agent_response.content

                        # Yield response
                        yield {
                            "run_id": run_id,
                            "session_id": session_id,
                            "agent_response": agent_response.to_dict(),
                            "success": True,
                            "final": False,
                            "stream_chunk_id": chunk_id,
                        }
                        chunk_id += 1

                    if agent_response.message_type == "task_result":
                        break

                # Store complete assistant response in Mem0 memory
                if assistant_response_content.strip():
                    await mem0_utils.store_message_in_mem0(
                        role="assistant",
                        content=assistant_response_content.strip(),
                        user_id=user_id,
                        agent_id=agent_id,
                        session_id=session_id,
                        agent=agent,
                    )

                # Send final response
                yield {
                    "run_id": run_id,
                    "session_id": session_id,
                    "agent_response": {
                        "content": "",
                        "message_type": "unknown",
                        "source": "unknown",
                        "models_usage": {"prompt_tokens": 0, "completion_tokens": 0},
                        "metadata": None,
                    },
                    "success": True,
                    "final": True,
                    "stream_chunk_id": chunk_id,
                }

            except Exception as e:
                self.logger.error(
                    f"Chat processing error for session {session_id}: {e}"
                )
                yield {
                    "run_id": run_id,
                    "session_id": session_id,
                    "agent_response": {"content": f"Error: {str(e)}"},
                    "success": False,
                    "final": True,
                    "stream_chunk_id": 0,
                    "error": str(e),
                }

    async def chat_with_agent_once(
        self,
        session_id: str,
        user_message: str,
        agent: AssistantAgent,
        run_id: Optional[str] = None,
        cancellation_token=None,
        attachments: Optional[List[MessageAttachment]] = None,
    ) -> Dict:
        """
        Process single chat interaction with improved error handling
        """
        self.logger.info(f"Starting single-agent chat for session: {session_id}")

        async with self._session_context(session_id):
            try:

                # Patch user proxy if needed
                if isinstance(agent, UserProxyAgent):
                    agent.input = self._create_user_input_patch(user_message)

                # Update session memory
                await self._update_session_memory(session_id, "user", user_message)

                # Create appropriate message type (text or multimodal)
                message = await self._create_message_from_content(
                    user_message, attachments
                )

                response = await agent.on_messages(
                    [message], cancellation_token=cancellation_token
                )

                # Create agent response
                agent_response = AgentResponse(
                    content=response.chat_message.content,
                    source=response.chat_message.source,
                    models_usage=self.message_processor.extract_models_usage(
                        response.chat_message
                    ),
                    message_type=getattr(response.chat_message, "type", "text"),
                    metadata=getattr(response.chat_message, "metadata", None),
                )

                # Update session memory
                await self._update_session_memory(
                    session_id, "assistant", agent_response.to_dict()
                )

                return {
                    "run_id": run_id,
                    "session_id": session_id,
                    "agent_response": agent_response.to_dict(),
                    "success": True,
                    "final": True,
                }

            except Exception as e:
                self.logger.error(
                    f"Single-agent chat error for session {session_id}: {e}"
                )
                return {
                    "run_id": run_id,
                    "session_id": session_id,
                    "agent_response": {"content": f"Error: {str(e)}"},
                    "success": False,
                    "final": True,
                    "error": str(e),
                }

    async def chat_with_direct_agent(
        self,
        agent: AssistantAgent,
        user_message: Union[str, Dict, List],
        run_id: Optional[str] = None,
        attachments: Optional[List[MessageAttachment]] = None,
    ) -> Dict:
        """
        Enhanced direct agent chat supporting all AutoGen message types

        Args:
            agent: The AutoGen agent to chat with
            user_message: User message content (str, dict, or list for complex)
            run_id: Optional run identifier
            attachments: Optional file attachments
            metadata: Optional metadata for structured messages

        Returns:
            Comprehensive response with all message type support
        """
        self.logger.info(f"Starting enhanced direct agent chat for run: {run_id}")

        # Handle different input message types
        messages_to_send = []
        processed_message = None

        try:

            if isinstance(user_message, str):
                processed_message = user_message

            # Create the main user message based on type and content
            if isinstance(user_message, dict):
                # Handle dictionary input (structured content)
                if "content" in user_message:
                    try:

                        class DynamicContent(BaseModel):
                            content: str = user_message.get("content", "")
                            data: Dict = user_message

                        structured_content = DynamicContent(
                            content=user_message.get("content", ""),
                            data=user_message,
                        )

                        main_message = StructuredMessage(
                            content=structured_content, source="user"
                        )
                    except Exception as e:
                        self.logger.warning(
                            f"Failed to create structured message from dict: {e}"
                        )
                        # Fallback to text message
                        main_message = TextMessage(
                            content=str(
                                user_message.get("content", json.dumps(user_message))
                            ),
                            source="user",
                        )
                else:
                    # Convert dict to text message
                    content = user_message.get("content", json.dumps(user_message))
                    main_message = TextMessage(content=content, source="user")

                messages_to_send.append(main_message)

            elif isinstance(user_message, list):
                # Handle list input (multiple content items)
                for content in user_message:
                    if isinstance(content, str):
                        message = TextMessage(content=content, source="user")
                        messages_to_send.append(message)
                    if isinstance(content, dict):
                        message = TextMessage(
                            content=json.dumps(content), source="user"
                        )
                        messages_to_send.append(message)

            else:
                # Handle string input (most common case)
                main_message = await self._create_message_from_content(
                    str(processed_message), attachments
                )
                messages_to_send.append(main_message)

            # Send messages to agent and get response
            response = await agent.on_messages(
                messages_to_send, cancellation_token=CancellationToken()
            )

            agent_response = AgentResponse(
                content=response.chat_message.content,
                source=response.chat_message.source,
                models_usage=self.message_processor.extract_models_usage(
                    response.chat_message
                ),
                message_type=getattr(response.chat_message, "type", "text"),
                metadata=getattr(response.chat_message, "metadata", None),
            )

            return {
                "run_id": run_id,
                "session_id": run_id,
                "agent_response": agent_response.to_dict(),
                "success": True,
                "final": True,
            }

        except Exception as e:
            self.logger.error(f"Direct agent chat error for run {run_id}: {e}")
            return {
                "run_id": run_id,
                "session_id": run_id,
                "agent_response": {},
                "error": str(e),
                "success": False,
                "final": True,
            }

    async def process_chat_stream(
        self, session_id: str, user_message: str
    ) -> AsyncGenerator[Dict, None]:
        """
        Optimized chat stream processing
        """
        try:
            # Get session data
            session_data = await self.session_manager.get_session_data(session_id)
            (
                agent_config,
                communication_type,
                memory,
                cancellation_token,
                organization_id,
                use_knowledge,
                variables,
                user_id,
                use_mem0_memory,
            ) = session_data

            # Initialize agent and team
            agent = await self.agent_factory.initialize_chat_session(
                run_id=session_id,
                agent_config=agent_config,
                session_memory=memory,
                organization_id=organization_id,
                use_knowledge=use_knowledge,
                variables=variables,
            )

            # Process and yield responses
            async for response in self.process_chat(
                session_id=session_id,
                user_message=user_message,
                agent=agent,
            ):
                if response.get("success", False) and not response.get("final", False):
                    yield response.get("agent_response", {"content": ""})

        except Exception as e:
            self.logger.error(
                f"Chat stream processing error for session {session_id}: {e}"
            )
            yield {"content": f"Error: {str(e)}"}

    async def end_chat_session(self, session_id: str) -> bool:
        """End chat session with improved cleanup"""
        try:
            async with self._session_lock:
                self._active_sessions.pop(session_id, None)

            await self.session_manager.delete_session(session_id)
            self.logger.info(f"Session {session_id} ended successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error ending session {session_id}: {e}")
            return False

    async def get_session_history(self, session_id: str) -> List[Dict]:
        """Get session history with error handling"""
        try:
            return await self.session_manager.get_session_history(session_id)
        except Exception as e:
            self.logger.error(f"Error getting session history for {session_id}: {e}")
            return []

    async def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs"""
        async with self._session_lock:
            return list(self._active_sessions.keys())

    async def force_cleanup_session(self, session_id: str):
        """Force cleanup of a session"""
        async with self._session_lock:
            self._active_sessions.pop(session_id, None)
        self.logger.info(f"Force cleaned up session: {session_id}")

    async def shutdown(self):
        """Graceful shutdown"""
        if hasattr(self, "_cleanup_task"):
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

        # Clean up all active sessions
        async with self._session_lock:
            self._active_sessions.clear()

        self.logger.info("ChatProcessor shutdown complete")

    def __del__(self):
        """Cleanup on destruction"""
        if hasattr(self, "_cleanup_task") and not self._cleanup_task.done():
            self._cleanup_task.cancel()
