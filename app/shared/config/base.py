import os
from dataclasses import dataclass
from functools import lru_cache

from dotenv import load_dotenv


@dataclass
class APIConfig:
    host: str = "0.0.0.0"
    port: int = 6000


@dataclass
class OpenAIConfig:
    api_key: str
    model: str = "gpt-4o-mini"


@dataclass
class RequestyConfig:
    api_key: str
    model: str = "gpt-4o-mini"
    base_url: str = "https://router.requesty.ai/v1"


@dataclass
class OpenRouterConfig:
    api_key: str
    model: str = "openai/gpt-4o-mini"
    base_url: str = "https://openrouter.ai/api/v1"
    site_url: str = ""  # Optional: Site URL for rankings on openrouter.ai
    site_name: str = ""  # Optional: Site name for rankings on openrouter.ai


@dataclass
class ApiGatewayConfig:
    api_url: str
    api_key: str
    organization_key: str


@dataclass
class WorkflowApiGatewayConfig:
    api_url: str
    api_key: str


@dataclass
class GitConfig:
    repo_url: str
    token: str
    branch: str


@dataclass
class KafkaConfig:
    kafka_bootstrap_servers: str
    kafka_agent_creation_topic: str
    kafka_agent_chat_topic: str
    kafka_agent_response_topic: str
    kafka_consumer_group: str
    kafka_agent_query_topic: str
    kafka_agent_message_topic: str
    kafka_agent_session_deletion_topic: str
    kafka_orchestration_team_session_topic: str
    kafka_orchestration_team_chat_topic: str
    kafka_human_input_request_topic: str
    kafka_human_input_response_topic: str
    kafka_agent_chat_stop_topic: str
    kafka_token_usage_topic: str


@dataclass
class RedisConfig:
    redis_host: str
    redis_port: int
    redis_db: int
    password: str | None = None


@dataclass
class AgentConfig:
    base_url: str
    auth_key: str
    test_user_id: str


@dataclass
class PineconeConfig:
    api_key: str
    environment: str = "us-east-1"
    index_name: str = "agent-memory"
    # dimension: int = 384  # Default for SentenceTransformer all-MiniLM-L6-v2
    dimension: int = 1536  # Default for OpenAI embeddings
    metric: str = "cosine"
    cloud: str = "aws"


@dataclass
class Settings:
    """Main configuration class that holds all config sections"""

    api: APIConfig
    openai: OpenAIConfig
    requesty: RequestyConfig
    openrouter: OpenRouterConfig
    git: GitConfig
    agent: AgentConfig
    kafka: KafkaConfig
    redis: RedisConfig
    gateway: ApiGatewayConfig
    workflow_api_gateway: WorkflowApiGatewayConfig
    pinecone: PineconeConfig
    environment: str = "development"
    ORCHESTRATION_TEAM_CHAT_MODEL_ID: str = ""
    # Model provider selection
    model_provider: str = "requesty"  # Options: "requesty", "openrouter", "openai"


# Add a function to clear the cache
def clear_settings_cache():
    """Clear the settings cache to force reload from environment variables"""
    get_settings.cache_clear()


@lru_cache()
def get_settings(force_reload=False) -> Settings:
    """
    Creates and returns a cached instance of Settings.
    Uses environment variables with fallbacks to default values.

    Args:
        force_reload: If True, will reload .env file (useful for testing)
    """
    # Force reload is just a dummy parameter to invalidate the cache when needed
    load_dotenv(override=True)  # Add override=True to ensure values are refreshed

    return Settings(
        api=APIConfig(
            host=os.getenv("API_HOST", "0.0.0.0"), port=int(os.getenv("API_PORT", 6000))
        ),
        openai=OpenAIConfig(
            api_key=os.getenv("OPENAI_API_KEY", ""),
            model=os.getenv("LLM_MODEL", "gpt-4o-mini"),
        ),
        requesty=RequestyConfig(
            api_key=os.getenv("REQUESTY_API_KEY", ""),
            model=os.getenv("LLM_MODEL", "gpt-4o-mini"),
            base_url=os.getenv("REQUESTY_BASE_URL", "https://router.requesty.ai/v1"),
        ),
        openrouter=OpenRouterConfig(
            api_key=os.getenv("OPENROUTER_API_KEY", ""),
            model=os.getenv("OPENROUTER_MODEL", "openai/gpt-4o-mini"),
            base_url=os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1"),
            site_url=os.getenv("OPENROUTER_SITE_URL", ""),
            site_name=os.getenv("OPENROUTER_SITE_NAME", ""),
        ),
        git=GitConfig(
            repo_url=os.getenv("REPO_URL", ""),
            token=os.getenv("GIT_TOKEN", ""),
            branch=os.getenv("ENV", "development"),
        ),
        agent=AgentConfig(
            base_url=os.getenv("AGENT_API_BASE_URL", ""),
            auth_key=os.getenv("AGENT_API_AUTH_KEY", ""),
            test_user_id=os.getenv("Test_User_Id", ""),
        ),
        kafka=KafkaConfig(
            kafka_bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", ""),
            kafka_agent_creation_topic=os.getenv("KAFKA_AGENT_CREATION_TOPIC", ""),
            kafka_agent_chat_topic=os.getenv("KAFKA_AGENT_CHAT_TOPIC", ""),
            kafka_agent_response_topic=os.getenv("KAFKA_AGENT_RESPONSE_TOPIC", ""),
            kafka_consumer_group=os.getenv("KAFKA_CONSUMER_GROUP", ""),
            kafka_agent_query_topic=os.getenv("KAFKA_AGENT_QUERY_TOPIC", ""),
            kafka_agent_message_topic=os.getenv("KAFKA_AGENT_MESSAGE_TOPIC", ""),
            kafka_agent_session_deletion_topic=os.getenv(
                "KAFKA_AGENT_SESSION_DELETION_TOPIC", ""
            ),
            kafka_orchestration_team_session_topic=os.getenv(
                "KAFKA_ORCHESTRATION_TEAM_SESSION_TOPIC",
                "orchestration_team_session_requests",
            ),
            kafka_orchestration_team_chat_topic=os.getenv(
                "KAFKA_ORCHESTRATION_TEAM_CHAT_TOPIC",
                "orchestration_team_chat_requests",
            ),
            kafka_human_input_request_topic=os.getenv(
                "KAFKA_HUMAN_INPUT_REQUEST_TOPIC", "human_input_requests"
            ),
            kafka_human_input_response_topic=os.getenv(
                "KAFKA_HUMAN_INPUT_RESPONSE_TOPIC", "human_input_responses"
            ),
            kafka_agent_chat_stop_topic=os.getenv(
                "KAFKA_AGENT_CHAT_STOP_TOPIC", "agent_chat_stop_requests"
            ),
            kafka_token_usage_topic=os.getenv(
                "KAFKA_TOKEN_USAGE_TOPIC", "token-usage-events"
            ),
        ),
        redis=RedisConfig(
            redis_host=os.getenv("REDIS_HOST", ""),
            redis_port=int(os.getenv("REDIS_PORT", 6379)),
            redis_db=int(os.getenv("REDIS_DB", 0)),
            password=os.getenv("REDIS_PASSWORD", None),
        ),
        gateway=ApiGatewayConfig(
            api_url=os.getenv("API_GATEWAY_URL", ""),
            api_key=os.getenv("API_GATEWAY_KEY", ""),
            organization_key=os.getenv("API_GATEWAY_ORGANIZATION_KEY", ""),
        ),
        workflow_api_gateway=WorkflowApiGatewayConfig(
            api_url=os.getenv("WORKFLOW_API_GATEWAY_URL", ""),
            api_key=os.getenv("WORKFLOW_API_GATEWAY_KEY", ""),
        ),
        pinecone=PineconeConfig(
            api_key=os.getenv("PINECONE_API_KEY", ""),
            environment=os.getenv("PINECONE_ENVIRONMENT", "us-east-1"),
            index_name=os.getenv("PINECONE_INDEX_NAME", "agent-memory"),
            dimension=int(os.getenv("PINECONE_DIMENSION", "1536")),
            metric=os.getenv("PINECONE_METRIC", "cosine"),
            cloud=os.getenv("PINECONE_CLOUD", "aws"),
        ),
        environment=os.getenv("ENV", "dev"),
        ORCHESTRATION_TEAM_CHAT_MODEL_ID=os.getenv(
            "ORCHESTRATION_TEAM_CHAT_MODEL_ID", ""
        ),
        model_provider=os.getenv("MODEL_PROVIDER", "requesty"),
    )
